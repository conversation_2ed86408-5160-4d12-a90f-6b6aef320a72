'use client';

import { UserProfile, UserProfileResponse } from '@/services/userService';
import { API_ENDPOINTS } from '.';

/**
 * Fetches the complete user profile from the backend API
 * @param token Access token for authorization
 * @returns Promise with the user profile response
 */
export async function getUserProfile(token: string): Promise<{
  ok: boolean;
  status: number;
  data?: UserProfile;
  error?: string;
}> {
  // console.log('getUserProfile - Starting fetch with token:', token);
  try {
    const response = await fetch(API_ENDPOINTS.USER_PROFILE, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 200,
        tags: ['user-profile'],
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to fetch user profile',
      };
    }

    const profileResponse: UserProfileResponse = await response.json();
    return {
      ok: true,
      status: response.status,
      data: profileResponse.data,
    };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while fetching user profile',
    };
  }
}

/**
 * Updates user profile information
 * @param token Access token for authorization
 * @param profileData Updated profile data
 * @returns Promise with the updated user profile response
 */
export async function updateUserProfile(
  token: string,
  idToken: string,
  profileData: { first_name: string; last_name: string; _id: string },
): Promise<{
  ok: boolean;
  status: number;
  data?: UserProfile;
  error?: string;
}> {
  console.log({ token, idToken });
  const payload = {
    user_data: {
      first_name: profileData.first_name,
      last_name: profileData.last_name,
      _id: profileData._id,
    },
  };
  try {
    const response = await fetch(API_ENDPOINTS.USERS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to update user profile',
      };
    }

    const profileResponse: UserProfileResponse = await response.json();
    return {
      ok: true,
      status: response.status,
      data: profileResponse.data,
    };
  } catch (error) {
    console.error('Error updating user profile:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while updating user profile',
    };
  }
}
