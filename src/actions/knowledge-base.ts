'use server';

import {
  CreateKnowledgeBaseApiResponse,
  CreateKnowledgeBaseInput,
  KnowledgeBaseApiResponse,
  KnowledgeBaseType,
} from '@/lib/types';
import { API_ENDPOINTS, QueryParams } from '.';

export async function getKnowledgeBases(
  clinicId: string,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: KnowledgeBaseApiResponse;
  error?: string;
}> {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(
      `${API_ENDPOINTS.RETELL_KNOWLEDGE_BASES}?clinic_id=${clinicId}`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          // 'x-id-token': idToken,
        },
        next: {
          revalidate: 6000,
          tags: [`knowledge-bases-${clinicId}`],
        },
      },
    );

    const data: KnowledgeBaseApiResponse = await response.json();

    return { ok: response.ok, status: response.status, data };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch knowledge bases. Please try again.',
    };
  }
}

export async function createKnowledgeBase(
  clinicId: string,
  data: CreateKnowledgeBaseInput,
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: CreateKnowledgeBaseApiResponse;
  error?: string;
}> {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(
      `${API_ENDPOINTS.RETELL_KNOWLEDGE_BASES}?clinic_id=${clinicId}`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'x-id-token': idToken,
        },
        body: JSON.stringify(data),
      },
    );

    const responseData = await response.json();

    return { ok: response.ok, status: response.status, data: responseData };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to create knowledge base. Please try again.',
    };
  }
}

export async function uploadKnowledgeBaseFiles(
  clinicId: string,
  token: string,
  files: FormData,
) {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: files,
    });

    const responseData = (await response.json()) as KnowledgeBaseType;

    return { ok: response.ok, status: response.status, data: responseData };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to upload files to knowledge base. Please try again.',
    };
  }
}
