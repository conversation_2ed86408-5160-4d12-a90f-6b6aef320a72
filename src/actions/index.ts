const BASE_URL = 'https://app.smartreception.ai/api';
// const BASE_URL = 'http://localhost:4000/api';

export const API_ENDPOINTS = {
  USER_SUPPORT_REQUEST: BASE_URL + '/users/support-request',
  ADMIN_CLINICS: BASE_URL + '/admin/clinics',
  CLINIC_DETAILS: BASE_URL + '/db/clinics/:clinic_id',
  CLINICS: BASE_URL + '/db/clinics',
  RETELL_ANALYTICS_LIST_CALLS: BASE_URL + '/retell/analytics/list-calls',
  DB_ANALYTICS_LIST_CALLS: BASE_URL + '/db/analytics/list-calls',
  ADMIN_TWILIO_PHONE_NUMBERS: BASE_URL + '/admin/twilio-phone-numbers',
  RETELL_KNOWLEDGE_BASES: BASE_URL + '/retell/knowledge-bases',
  RETELL_AGENT: BASE_URL + '/retell/agent',
  USER_PROFILE: BASE_URL + '/users/me',
  USER_INVITES: BASE_URL + '/db/user-invites',
  USER_INVITES_ACCEPT: BASE_URL + '/db/user-invites/accept',
  USERS: BASE_URL + '/users',
  CLINIC_OWNERS: BASE_URL + '/db/clinic-owners',
  ADMIN_USERS: BASE_URL + '/admin/users',
  ADMIN_ADMINS: BASE_URL + '/admin/admins',
  ADMIN_ANALYTICS_LIST_CALLS: BASE_URL + '/admin/analytics/list-calls',
  ADMIN_ASSIGN_TWILIO_NUMBER: BASE_URL + '/admin/retell/assign-twilio-number', //to assign twilio phone number to voice agent
  ADMIN_CLINIC: BASE_URL + '/admin/clinic', //to create or update a clinic for admin
  ADMIN_REGISTER_ADMIN: BASE_URL + '/admin/register-admin',
  ADMIN_UPDATE_PHONE_NUMBER: BASE_URL + '/admin//retell/update-phone-number', //to update twilio phone number
  ADMIN_CLINIC_OWNERS: BASE_URL + '/admin/clinic-owners',
  USER_CLINIC_ROLES: BASE_URL + '/db/user-clinic-roles',
};

export enum QueryParams {
  CLINIC_ID = 'clinic_id',
}
