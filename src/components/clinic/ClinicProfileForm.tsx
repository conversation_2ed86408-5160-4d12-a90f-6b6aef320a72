'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { Save, Loader2, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Form } from '@/components/ui/form';

import { ClinicType } from '@/lib/types';

import { BasicInformationCard } from './BasicInformationCard';
import { ClinicAddressesCard } from './ClinicAddressesCard';
import { SystemInformationCard } from './SystemInformationCard';
import { ClinicFormValues } from '@/lib/clinic-form-schema';

const clinicFormSchema = z.object({
  clinic_name: z.string().min(2, {
    message: 'Clinic name must be at least 2 characters.',
  }),
  clinic_email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  clinic_website: z.string().optional().or(z.literal('')),
  clinic_phone: z.string().optional(),
  human_transfer_destination_number: z.string().optional(),
  clinic_addresses: z
    .array(
      z.object({
        address: z.string().optional(),
        full_address: z.string().min(1, 'Full address is required'),
        business_location_id: z.string().optional(),
      }),
    )
    .optional(),
});

interface ClinicProfileFormProps {
  selectedClinic: ClinicType;
  onSubmitHandler: (values: ClinicFormValues) => Promise<void>;
}

export function ClinicProfileForm({
  selectedClinic,
  onSubmitHandler,
}: ClinicProfileFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof clinicFormSchema>>({
    resolver: zodResolver(clinicFormSchema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_website: '',
      clinic_phone: '',
      human_transfer_destination_number: '',
      clinic_addresses: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'clinic_addresses',
  });

  // Update form when selectedClinic changes
  useEffect(() => {
    if (selectedClinic) {
      const addresses =
        selectedClinic.clinic_addresses?.map((addr) => ({
          address: typeof addr === 'string' ? addr : addr.address || '',
          full_address:
            typeof addr === 'string'
              ? addr
              : addr.full_address || addr.address || '',
          business_location_id:
            typeof addr === 'string' ? '' : addr.business_location_id || '',
        })) || [];

      form.reset({
        clinic_name: selectedClinic.clinic_name,
        clinic_email: selectedClinic.clinic_email,
        clinic_website: selectedClinic.clinic_website,
        clinic_phone: selectedClinic.clinic_phone || '',
        human_transfer_destination_number:
          selectedClinic.human_transfer_destination_number || '',
        clinic_addresses: addresses,
      });

      setError(null);
    }
  }, [selectedClinic, form]);

  const onSubmit = async (values: z.infer<typeof clinicFormSchema>) => {
    setLoading(true);
    setError(null);
    try {
      await onSubmitHandler(values); // ✅ delegate everything
    } catch (err) {
      console.error(err);
      setError('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <BasicInformationCard form={form} />
          <ClinicAddressesCard
            form={form}
            fields={fields}
            append={append}
            remove={remove}
          />
          <SystemInformationCard selectedClinic={selectedClinic} />
          <div className="flex justify-end">
            <Button type="submit" disabled={loading} size="lg">
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
