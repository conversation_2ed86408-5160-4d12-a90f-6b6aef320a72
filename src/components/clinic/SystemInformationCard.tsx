'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

import { ClinicType } from '@/lib/types';
import { DiagnosticServicesTable } from './DiagnosticServicesTable';

interface SystemInformationCardProps {
  selectedClinic: ClinicType;
}

export function SystemInformationCard({
  selectedClinic,
}: SystemInformationCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>System Information</CardTitle>
        <CardDescription>
          Read-only system generated information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <FormLabel>Status</FormLabel>
            <div className="mt-2">
              <Badge
                variant={selectedClinic.is_active ? 'default' : 'secondary'}
              >
                {selectedClinic.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
          </div>

          <div>
            <FormLabel>Created At</FormLabel>
            <div className="mt-2">
              <Input
                value={new Date(selectedClinic.created_at).toLocaleDateString()}
                disabled
              />
            </div>
          </div>

          <div>
            <FormLabel>Updated At</FormLabel>
            <div className="mt-2">
              <Input
                value={new Date(selectedClinic.updated_at).toLocaleDateString()}
                disabled
              />
            </div>
          </div>
        </div>

        <div>
          <FormLabel>Clinic ID</FormLabel>
          <div className="mt-2">
            <Input
              value={selectedClinic._id}
              disabled
              className="font-mono text-sm"
            />
          </div>
        </div>

        {selectedClinic.diagnostic_services &&
          selectedClinic.diagnostic_services.length > 0 && (
            <DiagnosticServicesTable
              services={selectedClinic.diagnostic_services}
              title="Diagnostic Services"
              description="Services offered by this clinic"
            />
          )}
      </CardContent>
    </Card>
  );
}
