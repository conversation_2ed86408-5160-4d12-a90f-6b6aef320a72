import { useState } from 'react';
import { Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { API_ENDPOINTS } from '@/actions';

interface RemoveStaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff: {
    id: number;
    name: string;
    currentRole: string;
  } | null;
  onRemove: () => void;
  userId: string;
  clinicId: string;
  accessToken: string;
}

export function RemoveStaffDialog({
  open,
  onOpenChange,
  staff,
  onRemove,
  userId,
  clinicId,
  accessToken,
}: RemoveStaffDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  if (!staff) return null;

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(API_ENDPOINTS.USER_CLINIC_ROLES, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          user_id: userId,
          clinic_id: clinicId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage =
          data?.error?.message || 'Failed to remove staff member';
        throw new Error(errorMessage);
      }

      toast.success('Staff member removed successfully');
      onRemove();
      onOpenChange(false);
    } catch (error) {
      console.error('Error removing staff member:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to remove staff member',
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Remove Staff
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Delete the staff: {staff.name}
          </p>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <div className="space-y-2"></div>

          <div className="rounded-lg bg-red-50 p-4 text-sm text-red-700 dark:bg-red-900/30 dark:text-red-300">
            <div className="flex">
              <Info className="mr-2 h-4 w-4 flex-shrink-0 mt-0.5" />
              <span>
                Are you sure you want to remove {staff.name}? This action will
                permanently remove their access.
              </span>
            </div>
          </div>

          <div className="flex justify-end pt-2">
            <Button
              variant="destructive"
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? 'Removing...' : 'Remove'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
