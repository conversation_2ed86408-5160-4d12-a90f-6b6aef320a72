'use client';

import { <PERSON>, Phone, PhoneCall, PhoneMissed } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MetricCardSkeleton } from '@/components/blocks/analytics-skeletons';
import type { ApiData } from '@/lib/types';

interface LifetimeMetricCardsProps {
  analyticsData: ApiData | null;
  analyticsLoading: boolean;
}

export function LifetimeMetricCards({
  analyticsData,
  analyticsLoading,
}: LifetimeMetricCardsProps) {
  if (analyticsLoading) {
    return (
      <>
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
      </>
    );
  }

  const totalCalls = analyticsData?.calls?.length || 0;
  const successfulCalls =
    analyticsData?.calls?.filter((call) => call.call_analysis?.call_successful)
      .length || 0;
  const unsuccessfulCalls =
    analyticsData?.calls?.filter((call) => !call.call_analysis?.call_successful)
      .length || 0;

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
          <Phone className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">{totalCalls}</div>
          <p className="text-sm text-cyan-600 dark:text-cyan-400 mt-2">
            Lifetime Stats
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Successful Calls
          </CardTitle>
          <PhoneCall className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">
            {successfulCalls}
          </div>
          <p className="text-sm text-green-600 dark:text-green-400 mt-2">
            {totalCalls > 0
              ? `${Math.round((successfulCalls / totalCalls) * 100)}% success rate`
              : 'No data available'}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Unsuccessful Calls
          </CardTitle>
          <PhoneMissed className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">
            {unsuccessfulCalls}
          </div>
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            {totalCalls > 0
              ? `${Math.round((unsuccessfulCalls / totalCalls) * 100)}% unsuccessful rate`
              : 'No data available'}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Avg. Call Duration
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl md:text-3xl font-bold">
            {analyticsData?.summary
              ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
              : '0m'}
          </div>
          <p className="text-sm mt-2 text-muted-foreground">Average duration</p>
        </CardContent>
      </Card>
    </>
  );
}
