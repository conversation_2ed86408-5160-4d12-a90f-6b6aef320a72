'use client';

import { useState, useEffect } from 'react';
import {
  AlertCircle,
  FileText,
  Globe,
  Loader2,
  Plus,
  Upload,
  Check,
  X,
  File,
  Clock,
  Info,
  RefreshCw,
  Book,
} from 'lucide-react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  createKnowledgeBase,
  getKnowledgeBases,
} from '@/actions/knowledge-base';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import {
  CreateKnowledgeBaseInput,
  KnowledgeBaseApiResponse,
} from '@/lib/types';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { format, fromUnixTime } from 'date-fns';
import { uploadFilesWithProgress } from '@/lib/upload-helpers';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '../ui/badge';

const urlFormSchema = z.object({
  url: z.string().url('Please enter a valid URL'),
});

const textFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  text: z.string().min(10, 'Text should be at least 10 characters long'),
});

export default function KnowledgeBasePage() {
  const [knowledgeBases, setKnowledgeBases] = useState<
    KnowledgeBaseApiResponse['data']
  >([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [urls, setUrls] = useState<string[]>([]);
  const [texts, setTexts] = useState<{ title: string; text: string }[]>([]);
  const [files, setFiles] = useState<File[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [uploadingFiles, setUploadingFiles] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [success, setSuccess] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState('existing');

  const { getTokens } = useAuth();
  const { selectedClinic } = useAdminClinic();

  const urlForm = useForm<{ url: string }>({
    resolver: zodResolver(urlFormSchema),
    defaultValues: {
      url: '',
    },
  });

  const textForm = useForm<{ title: string; text: string }>({
    resolver: zodResolver(textFormSchema),
    defaultValues: {
      title: '',
      text: '',
    },
  });

  useEffect(() => {
    async function fetchKnowledgeBases() {
      try {
        setLoading(true);
        setError(null);

        const { accessToken, idToken } = await getTokens();
        if (!accessToken || !idToken) {
          setError('Either accessToken or idToken is not available');
          setLoading(false);
          return;
        }
        const clinicId = selectedClinic?._id;

        if (!clinicId) {
          setError('Clinic ID is required');
          setLoading(false);
          return;
        }

        const result = await getKnowledgeBases(clinicId, accessToken);
        console.log(result);

        if (result.ok && result.data) {
          setKnowledgeBases(
            // result.data.data
            Array.isArray(result.data.data)
              ? result.data.data
              : [result.data.data],
          );
        } else {
          setError(
            result.error ||
              'Failed to fetch knowledge bases or knowledge base not created yet',
          );
          setKnowledgeBases([]);
        }
      } catch (err) {
        console.error('Error fetching knowledge bases:', err);
        setError('An unexpected error occurred');
        setKnowledgeBases([]);
      } finally {
        setLoading(false);
      }
    }

    fetchKnowledgeBases();
  }, [selectedClinic, getTokens]);

  const formatTimestamp = (timestamp: number) => {
    try {
      const date =
        timestamp > 9999999999
          ? fromUnixTime(timestamp / 1000)
          : fromUnixTime(timestamp);
      return format(date, 'MMM dd, yyyy HH:mm:ss');
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Invalid date';
    }
  };

  const handleAddUrl = (data: { url: string }) => {
    setUrls([...urls, data.url]);
    urlForm.reset();
  };

  const handleAddText = (data: { title: string; text: string }) => {
    setTexts([...texts, data]);
    textForm.reset();
  };

  const handleRemoveUrl = (index: number) => {
    setUrls(urls.filter((_, i) => i !== index));
  };

  const handleRemoveText = (index: number) => {
    setTexts(texts.filter((_, i) => i !== index));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const fileList = Array.from(event.target.files);
      setFiles((prevFiles) => [...prevFiles, ...fileList]);
    }
  };

  const handleRemoveFile = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const handleCreateKnowledgeBase = async () => {
    try {
      setSubmitting(true);
      setSuccess(null);
      setError(null);

      if (urls.length === 0 && texts.length === 0) {
        setError('Please add at least one URL or text content');
        setSubmitting(false);
        return;
      }

      const { accessToken, idToken } = await getTokens();
      if (!accessToken || !idToken) {
        setError('Either accessToken or idToken is not available');
        setLoading(false);
        return;
      }
      const clinicId = selectedClinic?._id;

      if (!clinicId) {
        setError('Clinic ID is required');
        setSubmitting(false);
        return;
      }

      const data: CreateKnowledgeBaseInput = {
        urls: urls,
        texts: texts,
      };

      const result = await createKnowledgeBase(
        clinicId,
        data,
        accessToken,
        idToken,
      );

      console.log(result);

      if (result.ok && result.data) {
        setSuccess('Knowledge base created successfully');
        setUrls([]);
        setTexts([]);
        const refreshResult = await getKnowledgeBases(clinicId, accessToken);
        if (refreshResult.ok && refreshResult.data) {
          setKnowledgeBases(
            Array.isArray(refreshResult.data.data)
              ? refreshResult.data.data
              : [refreshResult.data.data],
          );
        }
      } else {
        setError(result.error || 'Failed to create knowledge base');
      }
    } catch (err) {
      console.error('Error creating knowledge base:', err);
      setError(error || 'An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUploadFiles = async () => {
    if (files.length === 0) {
      setError('Please select files to upload');
      return;
    }

    try {
      setUploadingFiles(true);
      setSuccess(null);
      setError(null);
      setUploadProgress(0);

      const { accessToken, idToken } = await getTokens();
      if (!accessToken || !idToken) {
        setError('Either accessToken or idToken is not available');
        setLoading(false);
        return;
      }
      const clinicId = selectedClinic?._id;

      if (!clinicId) {
        setError('Clinic ID is required');
        setUploadingFiles(false);
        return;
      }

      const formData = new FormData();
      files.forEach((file) => {
        formData.append('files', file);
      });

      // Use uploadFilesWithProgress instead of uploadKnowledgeBaseFiles
      const url = `https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=${clinicId}`;
      const result = await uploadFilesWithProgress(
        url,
        formData,
        accessToken,
        (progress) => {
          setUploadProgress(progress);
        },
      );

      if (result.ok && result.data) {
        setSuccess('Files uploaded successfully');
        setFiles([]);

        const refreshResult = await getKnowledgeBases(clinicId, accessToken);
        if (refreshResult.ok && refreshResult.data) {
          setKnowledgeBases(
            Array.isArray(refreshResult.data.data)
              ? refreshResult.data.data
              : [refreshResult.data.data],
          );
        }
      } else {
        setError(result.error || 'Failed to upload files');
      }
    } catch (err) {
      console.error('Error uploading files:', err);
      setError('An unexpected error occurred');
    } finally {
      setUploadingFiles(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Knowledge Base</h1>
          <p className="text-muted-foreground mt-1">
            Manage your clinic&apos;s knowledge bases for voice agents
          </p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="default" className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            {success}
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={tabValue} onValueChange={setTabValue} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="existing">
            <FileText className="mr-2 h-4 w-4" />
            Existing Knowledge Bases
          </TabsTrigger>
          <TabsTrigger value="create">
            <Plus className="mr-2 h-4 w-4" />
            Create New
          </TabsTrigger>
        </TabsList>

        <TabsContent value="existing" className="space-y-4 mt-4">
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : knowledgeBases.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {knowledgeBases.map((kb) => (
                <Card key={kb.knowledge_base_id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xl truncate break-all">
                        {kb.knowledge_base_name}
                      </CardTitle>
                      <Badge
                        variant={
                          kb.status === 'ready' ? 'default' : 'secondary'
                        }
                      >
                        {kb.status}
                      </Badge>
                    </div>
                    <CardDescription>
                      <div className="flex items-center space-x-2 mt-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Last Modified:{' '}
                          {formatTimestamp(Number(kb.user_modified_timestamp))}
                        </span>
                      </div>
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground" />
                      <span>ID: {kb.knowledge_base_id}</span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <RefreshCw className="h-4 w-4 text-muted-foreground" />
                      <span>
                        Auto Refresh:{' '}
                        {kb.enable_auto_refresh ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mt-2">
                        Sources:
                      </h4>
                      <ul className="mt-1 list-disc list-inside text-sm space-y-1">
                        {kb.knowledge_base_sources
                          ? kb.knowledge_base_sources.map((source) => (
                              <li key={source.source_id}>
                                {source.filename ? (
                                  <>
                                    <strong>{source.filename}</strong> (
                                    {source.type},{' '}
                                    {(source.file_size! / 1024).toFixed(1)} KB)
                                  </>
                                ) : source.title ? (
                                  <>
                                    <strong>{source.title}</strong> (
                                    {source.type})
                                  </>
                                ) : (
                                  <em>Unknown source</em>
                                )}
                              </li>
                            ))
                          : null}
                      </ul>
                    </div>
                  </CardContent>

                  {/* <CardFooter className="pt-3 pb-3">
                    <Button variant="outline" size="sm" className="w-full">
                      <Pencil className="h-4 w-4 mr-2" /> Edit Knowledge Base
                    </Button>
                  </CardFooter> */}
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Book className="h-16 w-16 text-muted-foreground mb-4" />
                <p className="text-xl font-medium text-center">
                  No Knowledge Bases Found
                </p>
                <p className="text-muted-foreground text-center mt-1">
                  Create your first knowledge base to enhance your voice agent
                </p>
                <Button
                  className="mt-4"
                  // onClick={() => {
                  //   const tabsList = document.querySelector('[role="tablist"]');
                  //   if (tabsList) {
                  //     const createTab = tabsList.querySelector(
                  //       '[value="create"]',
                  //     ) as HTMLButtonElement;
                  //     if (createTab) createTab.click();
                  //   }
                  // }}
                  onClick={() => setTabValue('create')}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create Knowledge Base
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="create" className="space-y-6 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ">
            <Card>
              <CardHeader>
                <CardTitle>Add URLs</CardTitle>
                <CardDescription>
                  Add web pages to be included in your knowledge base
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...urlForm}>
                  <form
                    onSubmit={urlForm.handleSubmit(handleAddUrl)}
                    className="flex items-end space-x-2"
                  >
                    <div className="flex-1">
                      <FormField
                        control={urlForm.control}
                        name="url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>URL</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="https://example.com/page"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <Button type="submit" className="flex-shrink-0">
                      <Plus className="mr-2 h-4 w-4" />
                      Add URL
                    </Button>
                  </form>
                </Form>

                {urls.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">Added URLs</h4>
                    <div className="space-y-2">
                      {urls.map((url, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
                        >
                          <div className="flex items-center space-x-2 truncate">
                            <Globe className="h-4 w-4 text-primary" />
                            <span className="truncate">{url}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveUrl(index)}
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upload Documents</CardTitle>
                <CardDescription>
                  Upload TXT and PDF files to be included in your knowledge base
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-4">
                  <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center hover:border-primary transition-colors">
                    <label
                      htmlFor="fileUpload"
                      className="flex flex-col items-center justify-center cursor-pointer"
                    >
                      <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                      <p className="text-lg font-medium">
                        Click to select files or drag and drop
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        Supported file types: PDF, TXT (Max 10MB per file)
                      </p>
                      <input
                        id="fileUpload"
                        type="file"
                        className="hidden"
                        multiple
                        accept=".pdf,.txt"
                        onChange={handleFileChange}
                        aria-label="Upload files"
                      />
                    </label>
                  </div>

                  {files.length > 0 && (
                    <div className="mt-2">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Selected Files</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleUploadFiles}
                          disabled={uploadingFiles}
                        >
                          {uploadingFiles ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Uploading...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Files
                            </>
                          )}
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {files.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                          >
                            <div className="flex items-center space-x-3 truncate">
                              <File className="h-5 w-5 text-primary" />
                              <div className="overflow-hidden">
                                <p className="font-medium truncate">
                                  {file.name}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {(file.size / 1024).toFixed(2)} KB
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveFile(index)}
                            >
                              <X className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {uploadingFiles && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Uploading files...</span>
                        <span>{uploadProgress}%</span>
                      </div>
                      <Progress value={uploadProgress} className="h-2" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Add Text Content</CardTitle>
              <CardDescription>
                Add custom text content to be included in your knowledge base
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...textForm}>
                <form
                  onSubmit={textForm.handleSubmit(handleAddText)}
                  className="space-y-4"
                >
                  <FormField
                    control={textForm.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Content Title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={textForm.control}
                    name="text"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Text Content</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter your content here..."
                            className="min-h-32"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Text Content
                  </Button>
                </form>
              </Form>

              {texts.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Added Text Content</h4>
                  <div className="space-y-2">
                    {texts.map((textItem, index) => (
                      <div key={index} className="p-2 bg-gray-50 rounded-md">
                        <div className="flex items-center justify-between mb-1">
                          <div className="font-medium">{textItem.title}</div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveText(index)}
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                        <div className="text-sm text-gray-700 truncate">
                          {textItem.text.length > 100
                            ? `${textItem.text.substring(0, 100)}...`
                            : textItem.text}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button
              onClick={handleCreateKnowledgeBase}
              disabled={submitting || (urls.length === 0 && texts.length === 0)}
              className="w-full sm:w-auto"
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Create Knowledge Base
                </>
              )}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
