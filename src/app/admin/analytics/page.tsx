'use client';

import { useState, useEffect, useMemo } from 'react';
import { RefreshCw } from 'lucide-react';
import { format, subDays } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Tabs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

// Import context and API functions
import { useAuth } from '@/contexts/AuthContext';
import type { ApiData, Call, ProcessedCallData } from '@/lib/types';

// Import analytics components
import {
  LifetimeMetricCards,
  DailyCallChart,
  SentimentChart,
  HourlyCallChart,
  CallDurationChart,
  WeeklyCallChart,
  MonthlyCallChart,
  AverageDurationChart,
  CallVolumeHeatmap,
  RecentCallsTable,
} from '@/components/analytics';
import { Card } from '@/components/ui/card';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { fetchAdminAnalyticsData } from '@/actions/admin';

// Time period type
type TimePeriod = 'day' | 'week' | 'month' | 'quarter' | 'year';

export default function CallAnalyticsPage() {
  const [timePeriod] = useState<TimePeriod>('month');
  const [analyticsData, setAnalyticsData] = useState<ApiData | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  // Add state for lifetime analytics data
  const [lifetimeAnalyticsData, setLifetimeAnalyticsData] =
    useState<ApiData | null>(null);
  const [lifetimeAnalyticsLoading, setLifetimeAnalyticsLoading] =
    useState(false);

  const { selectedClinic } = useAdminClinic();
  const { getTokens } = useAuth();

  const processCallsData = useMemo(() => {
    return (calls: Call[], period: 'day' | 'week' | 'month') => {
      if (!calls || calls.length === 0) {
        return [];
      }

      const now = new Date();
      const dataMap = new Map<string, ProcessedCallData>();
      const orderedKeys: string[] = [];

      if (period === 'day') {
        // Last 24 hours, grouped by hour
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i, 0, 0, 0);
          const key = hour.getHours().toString().padStart(2, '0') + ':00';
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const diffMs = now.getTime() - callDate.getTime();
          const diffHours = diffMs / (1000 * 60 * 60);

          if (diffHours >= 0 && diffHours < 24) {
            const hourKey =
              callDate.getHours().toString().padStart(2, '0') + ':00';
            const data = dataMap.get(hourKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            }
          }
        });
      } else if (period === 'week') {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: days[date.getDay()],
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          if (diffDays >= 0 && diffDays < 7) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            }
          }
        });
      } else {
        // Monthly - last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          if (diffDays >= 0 && diffDays < 30) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
            }
          }
        });
      }

      return orderedKeys.map((key) => dataMap.get(key)!);
    };
  }, []);

  const processHourlyData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const hourlyMap = new Map<number, number>();

      // Initialize all hours
      for (let i = 0; i < 24; i++) {
        hourlyMap.set(i, 0);
      }

      calls.forEach((call) => {
        const callDate = new Date(call.start_timestamp);
        if (isNaN(callDate.getTime())) return;

        const hour = callDate.getHours();
        hourlyMap.set(hour, (hourlyMap.get(hour) || 0) + 1);
      });

      return Array.from(hourlyMap.entries())
        .map(([hour, calls]) => ({
          name:
            hour === 0
              ? '12 AM'
              : hour === 12
                ? '12 PM'
                : hour < 12
                  ? `${hour} AM`
                  : `${hour - 12} PM`,
          calls,
        }))
        .filter((_, index) => index >= 8 && index <= 19); // 8 AM to 7 PM
    };
  }, []);

  const processDurationData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const durationCounts = {
        '< 1 min': 0,
        '1-3 min': 0,
        '3-5 min': 0,
        '5-10 min': 0,
        '> 10 min': 0,
      };

      calls.forEach((call) => {
        const durationMinutes = call.duration_ms / (1000 * 60);

        if (durationMinutes < 1) {
          durationCounts['< 1 min']++;
        } else if (durationMinutes < 3) {
          durationCounts['1-3 min']++;
        } else if (durationMinutes < 5) {
          durationCounts['3-5 min']++;
        } else if (durationMinutes < 10) {
          durationCounts['5-10 min']++;
        } else {
          durationCounts['> 10 min']++;
        }
      });

      return Object.entries(durationCounts).map(([name, value]) => ({
        name,
        value,
      }));
    };
  }, []);

  const processSentimentData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const sentimentCounts = {
        Positive: 0,
        Neutral: 0,
        Negative: 0,
        Unknown: 0,
      };

      calls.forEach((call) => {
        const sentiment = call.call_analysis?.user_sentiment?.toLowerCase();
        if (sentiment === 'positive') {
          sentimentCounts['Positive']++;
        } else if (sentiment === 'negative') {
          sentimentCounts['Negative']++;
        } else if (sentiment === 'neutral') {
          sentimentCounts['Neutral']++;
        } else {
          sentimentCounts['Unknown']++;
        }
      });

      return Object.entries(sentimentCounts).map(([name, value]) => ({
        name,
        value,
        fill:
          name === 'Positive'
            ? '#10b981'
            : name === 'Negative'
              ? '#ef4444'
              : name === 'Neutral'
                ? '#f59e0b'
                : '#6b7280',
      }));
    };
  }, []);

  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      if (!selectedClinic?._id) return;

      setAnalyticsLoading(true);
      try {
        const { accessToken, idToken } = await getTokens();
        if (!accessToken || !idToken) {
          console.error('Either access or id token not available');
          setAnalyticsLoading(false);
          return;
        }

        // Calculate date range based on time period
        const endDate = new Date();
        let startDate = new Date();

        switch (timePeriod) {
          case 'day':
            startDate = subDays(endDate, 1);
            break;
          case 'week':
            startDate = subDays(endDate, 7);
            break;
          case 'month':
            startDate = subDays(endDate, 30);
            break;
          case 'quarter':
            startDate = subDays(endDate, 90);
            break;
          case 'year':
            startDate = subDays(endDate, 365);
            break;
        }

        const response = await fetchAdminAnalyticsData(
          accessToken,
          idToken,
          selectedClinic._id,
          startDate.toISOString(),
          endDate.toISOString(),
        );
        if (response.ok && response.data) {
          setAnalyticsData(response.data);
        } else {
          console.error('Failed to fetch analytics data:', response.error);
          setAnalyticsData(null);
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        setAnalyticsData(null);
      } finally {
        setAnalyticsLoading(false);
      }
    };

    fetchData();
  }, [selectedClinic?._id, getTokens, timePeriod]);

  // Fetch lifetime analytics data (2 years)
  useEffect(() => {
    const fetchLifetimeData = async () => {
      if (!selectedClinic?._id) return;

      setLifetimeAnalyticsLoading(true);
      try {
        const { accessToken, idToken } = await getTokens();
        if (!accessToken || !idToken) {
          console.error('Either access or id token not available');
          setAnalyticsLoading(false);
          return;
        }

        // Calculate date range for 2 years (730 days)
        const endDate = new Date();
        const startDate = subDays(endDate, 730);

        const response = await fetchAdminAnalyticsData(
          accessToken,
          idToken,
          selectedClinic._id,
          startDate.toISOString(),
          endDate.toISOString(),
        );

        if (response.ok && response.data) {
          setLifetimeAnalyticsData(response.data);
        } else {
          console.error(
            'Failed to fetch lifetime analytics data:',
            response.error,
          );
          setLifetimeAnalyticsData(null);
        }
      } catch (error) {
        console.error('Error fetching lifetime analytics data:', error);
        setLifetimeAnalyticsData(null);
      } finally {
        setLifetimeAnalyticsLoading(false);
      }
    };

    fetchLifetimeData();
  }, [selectedClinic?._id, getTokens]);

  // Process data for charts
  const {
    dailyCallData,
    weeklyCallData,
    monthlyCallData,
    hourlyCallData,
    callDurationData,
    sentimentData,
    recentCalls,
  } = useMemo(() => {
    const calls = analyticsData?.calls || [];
    return {
      dailyCallData: processCallsData(calls, 'day'),
      weeklyCallData: processCallsData(calls, 'week'),
      monthlyCallData: processCallsData(calls, 'month'),
      hourlyCallData: processHourlyData(calls),
      callDurationData: processDurationData(calls),
      sentimentData: processSentimentData(calls),
      recentCalls: calls.slice(0, 10).map((call, index) => ({
        id: index + 1,
        call_id: call.call_id,
        name: call.call_id.slice(5, 20) + '...',
        time: format(new Date(call.start_timestamp), 'd MMM, h:mm a'),
        duration: `${Math.floor(call.duration_ms / 60000)}m ${Math.floor((call.duration_ms % 60000) / 1000)}s`,
        status: call.call_analysis?.call_successful ? 'answered' : 'missed',
        recording_url: call.recording_url,
        public_log_url: call.public_log_url,
        sentiment: call.call_analysis?.user_sentiment || 'Unknown',
      })),
    };
  }, [
    analyticsData,
    processCallsData,
    processHourlyData,
    processDurationData,
    processSentimentData,
  ]);

  const handleRefresh = () => {
    // Trigger a refresh by updating the dependency
    if (selectedClinic?._id) {
      setAnalyticsLoading(true);
      setLifetimeAnalyticsLoading(true);
      // The useEffect will handle the actual refresh
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Call Analytics</h1>
        <div className="mt-2 sm:mt-0 flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={analyticsLoading || lifetimeAnalyticsLoading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${analyticsLoading || lifetimeAnalyticsLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          {/* <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button> */}
        </div>
      </div>

      {/* Lifetime Stats Section */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Lifetime Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <LifetimeMetricCards
            analyticsData={lifetimeAnalyticsData}
            analyticsLoading={lifetimeAnalyticsLoading}
          />
        </div>
      </div>

      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">Daily</TabsTrigger>
          <TabsTrigger value="weekly">Weekly</TabsTrigger>
          <TabsTrigger value="monthly">Monthly</TabsTrigger>
        </TabsList>

        <TabsContent value="daily" className="space-y-4">
          {/* Daily Analytics Metrics */}
          <Card>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">
                  {dailyCallData.reduce((sum, day) => sum + day.calls, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Total Calls</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {analyticsData?.summary
                    ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
                    : '0m'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Average Call Duration
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {dailyCallData.reduce((sum, day) => sum + day.successful, 0)}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Successful Calls
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {dailyCallData.reduce((sum, day) => sum + day.calls, 0) > 0
                    ? `${Math.round((dailyCallData.reduce((sum, day) => sum + day.successful, 0) / dailyCallData.reduce((sum, day) => sum + day.calls, 0)) * 100)}%`
                    : '0%'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Success Ratio
                </div>
              </div>
            </div>
          </Card>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
            <DailyCallChart data={dailyCallData} loading={analyticsLoading} />
            <SentimentChart data={sentimentData} loading={analyticsLoading} />
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <HourlyCallChart data={hourlyCallData} loading={analyticsLoading} />
            <CallDurationChart
              data={callDurationData}
              loading={analyticsLoading}
            />
          </div>
        </TabsContent>

        <TabsContent value="weekly" className="space-y-4">
          {/* Weekly Analytics Metrics */}
          <Card>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">
                  {weeklyCallData.reduce((sum, week) => sum + week.calls, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Total Calls</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {analyticsData?.summary
                    ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
                    : '0m'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Average Call Duration
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {weeklyCallData.reduce(
                    (sum, week) => sum + week.successful,
                    0,
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Successful Calls
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {weeklyCallData.reduce((sum, week) => sum + week.calls, 0) > 0
                    ? `${Math.round((weeklyCallData.reduce((sum, week) => sum + week.successful, 0) / weeklyCallData.reduce((sum, week) => sum + week.calls, 0)) * 100)}%`
                    : '0%'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Success Ratio
                </div>
              </div>
            </div>
          </Card>

          <WeeklyCallChart data={weeklyCallData} loading={analyticsLoading} />
        </TabsContent>

        <TabsContent value="monthly" className="space-y-4">
          <Card>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">
                  {monthlyCallData.reduce((sum, month) => sum + month.calls, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Total Calls</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {analyticsData?.summary
                    ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)}m`
                    : '0m'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Average Call Duration
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {monthlyCallData.reduce(
                    (sum, month) => sum + month.successful,
                    0,
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Successful Calls
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {monthlyCallData.reduce(
                    (sum, month) => sum + month.calls,
                    0,
                  ) > 0
                    ? `${Math.round((monthlyCallData.reduce((sum, month) => sum + month.successful, 0) / monthlyCallData.reduce((sum, month) => sum + month.calls, 0)) * 100)}%`
                    : '0%'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Success Ratio
                </div>
              </div>
            </div>
          </Card>

          <MonthlyCallChart data={monthlyCallData} loading={analyticsLoading} />
        </TabsContent>
      </Tabs>

      <div className="grid gap-4 md:grid-cols-2">
        <AverageDurationChart
          hourlyData={hourlyCallData}
          analyticsData={analyticsData}
          loading={analyticsLoading}
        />
        <CallVolumeHeatmap data={hourlyCallData} loading={analyticsLoading} />
      </div>

      <RecentCallsTable data={recentCalls} loading={analyticsLoading} />
    </div>
  );
}
