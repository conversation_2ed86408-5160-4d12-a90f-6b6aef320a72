'use client';

import { useState, useEffect } from 'react';
import {
  Phone,
  Loader2,
  AlertCircle,
  Check,
  X,
  MessageSquare,
  MoreHorizontal,
  Search,
} from 'lucide-react';
import { TwilioPhoneNumber } from '@/lib/twilio-types';
import { getTwilioPhoneNumbers } from '@/actions/admin';
import { TwilioNumberDetails } from '@/components/admin/TwilioNumberDetails';
import { format, parseISO } from 'date-fns';
import { fetchAuthSession } from 'aws-amplify/auth';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function TwilioNumbersPage() {
  const [phoneNumbers, setPhoneNumbers] = useState<TwilioPhoneNumber[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedNumber, setSelectedNumber] =
    useState<TwilioPhoneNumber | null>(null);
  const [detailsOpen, setDetailsOpen] = useState<boolean>(false);

  useEffect(() => {
    async function fetchTwilioNumbers() {
      try {
        setLoading(true);
        setError(null);

        // Get token from Amplify auth session
        const session = await fetchAuthSession({ forceRefresh: true });
        const token = session.tokens?.accessToken?.toString() || '';
        const idToken = session.tokens?.idToken?.toString() || '';

        const result = await getTwilioPhoneNumbers(token, idToken);

        if (result.ok && result.data) {
          setPhoneNumbers(result.data.data);
        } else {
          setError(result.error || 'Failed to load Twilio phone numbers');
        }
      } catch (err) {
        console.error('Error fetching Twilio numbers:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }

    fetchTwilioNumbers();
  }, []);

  const filteredNumbers = phoneNumbers.filter((number) => {
    if (!searchQuery) return true;

    return (
      number.phoneNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      number.friendlyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      number.status.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">
          Twilio Phone Numbers
        </h1>
        <Button>
          <Phone className="mr-2 h-4 w-4" />
          Add New Number
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center p-12">
          <Loader2 className="h-8 w-8 animate-spin mr-2 text-primary" />
          <p>Loading Twilio phone numbers...</p>
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Phone Numbers</CardTitle>
            <CardDescription>
              Manage your Twilio phone numbers used for Smart Reception
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex w-full max-w-sm items-center space-x-2">
                <Input
                  placeholder="Search phone numbers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-9"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 px-4 shrink-0"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Phone Number</TableHead>
                      <TableHead>Capabilities</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredNumbers.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className="text-center py-8 text-muted-foreground"
                        >
                          No phone numbers found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredNumbers.map((number) => (
                        <TableRow
                          key={number.sid}
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => {
                            setSelectedNumber(number);
                            setDetailsOpen(true);
                          }}
                        >
                          <TableCell className="font-medium">
                            <div className="flex flex-col">
                              <div>{number.friendlyName}</div>
                              <div className="text-sm text-muted-foreground">
                                {number.phoneNumber}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1 flex-wrap">
                              {number.capabilities.voice && (
                                <Badge
                                  variant="outline"
                                  className="bg-blue-50 text-blue-700 border-blue-200"
                                >
                                  <Phone className="h-3 w-3 mr-1" /> Voice
                                </Badge>
                              )}
                              {number.capabilities.sms && (
                                <Badge
                                  variant="outline"
                                  className="bg-green-50 text-green-700 border-green-200"
                                >
                                  <MessageSquare className="h-3 w-3 mr-1" /> SMS
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {number.status === 'in-use' ? (
                              <Badge
                                variant="outline"
                                className="bg-emerald-50 text-emerald-700 border-emerald-200"
                              >
                                <Check className="h-3 w-3 mr-1" /> Active
                              </Badge>
                            ) : (
                              <Badge
                                variant="outline"
                                className="bg-amber-50 text-amber-700 border-amber-200"
                              >
                                <X className="h-3 w-3 mr-1" /> Inactive
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {formatDate(number.dateCreated)}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedNumber(number);
                                    setDetailsOpen(true);
                                  }}
                                >
                                  <Phone className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                {/* <DropdownMenuItem>
                                  <Globe className="mr-2 h-4 w-4" />
                                  <span>Configure Webhooks</span>
                                </DropdownMenuItem> */}
                                <DropdownMenuSeparator />
                                {/* <DropdownMenuItem>
                                  <ExternalLink className="mr-2 h-4 w-4" />
                                  <span>Open in Twilio Console</span>
                                </DropdownMenuItem> */}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Twilio Number Details Dialog */}
      <TwilioNumberDetails
        number={selectedNumber}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
      />
    </div>
  );
}
