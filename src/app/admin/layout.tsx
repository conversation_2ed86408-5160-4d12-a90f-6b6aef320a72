'use client';

import type React from 'react';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Building2,
  ChevronDown,
  Home,
  LogOut,
  Menu,
  Phone,
  PhoneCall,
  Users,
  Shield,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { AdminClinicProvider } from '@/contexts/AdminClinicContext';
import { AdminClinicDropdown } from '@/components/ui/admin-clinic-dropdown';
import { Logo } from '@/components/ui/logo';
import { getUserProfile } from '@/actions/user';
import { UserProfile } from '@/services/userService';
import { Skeleton } from '@/components/ui/skeleton';

interface AdminDashboardLayoutProps {
  children: React.ReactNode;
}

export default function AdminDashboardLayout({
  children,
}: AdminDashboardLayoutProps) {
  const pathname = usePathname();
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const { logout, getTokens } = useAuth();

  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoadingProfile(true);
      try {
        const { accessToken, idToken } = await getTokens();
        if (!accessToken || !idToken) {
          console.error('No access token available');
          return;
        }
        const result = await getUserProfile(accessToken);
        if (result.ok && result.data) {
          setUserProfile(result.data);
        }
      } catch (error) {
        console.error('Error fetching the user data', error);
      } finally {
        setIsLoadingProfile(false);
      }
    };
    fetchUserProfile();
  }, [getTokens]);

  const getInitials = (firstName?: string, lastName?: string) => {
    if (!firstName || !lastName) return 'AU';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getDisplayName = () => {
    if (!userProfile) return 'Loading...';
    return `${userProfile.first_name} ${userProfile.last_name}`;
  };

  const navigation = [
    { name: 'Dashboard', href: '/admin/dashboard', icon: Home },
    { name: 'Manage Clinics', href: '/admin/clinics', icon: Building2 },
    { name: 'Manage Users', href: '/admin/users', icon: Users },
    { name: 'Manage Admins', href: '/admin/admins', icon: Shield },
    { name: 'Call Analytics', href: '/admin/analytics', icon: Phone },
    { name: 'Twilio Numbers', href: '/admin/twilio-numbers', icon: PhoneCall },
  ];

  return (
    <ProtectedRoute requireAdmin={true}>
      <AdminClinicProvider>
        <div className="flex h-screen">
          {/* Sidebar for desktop */}
          <div className="hidden md:flex md:w-64 md:flex-col">
            <div className="flex flex-col flex-grow border-r pt-5">
              <div className="flex items-center flex-shrink-0 px-4">
                <Logo />
              </div>
              <div className="mt-5 flex-grow flex flex-col">
                <nav className="flex-1 space-y-1 px-2">
                  <Link
                    href={'/dashboard'}
                    className="text-lg font-semibold bg-muted/60 py-2 px-4 truncate text-ellipsis line-clamp-1 text-foreground border-l-4 rounded-r border-primary mb-4 inline-flex items-center w-full"
                  >
                    <Home className="h-5 w-5 mr-2" /> Clinic Dashboard
                  </Link>
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        pathname === item.href
                          ? 'bg-muted text-foreground'
                          : 'text-muted-foreground hover:bg-accent hover:text-foreground',
                        'group flex items-center px-2 py-2 text-sm font-medium rounded-md',
                      )}
                    >
                      <item.icon
                        className={cn(
                          pathname === item.href
                            ? 'text-muted-foreground/70'
                            : 'text-muted-foreground/50 group-hover:text-muted-foreground/70',
                          'mr-3 h-5 w-5 flex-shrink-0',
                        )}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
              <div className="flex-shrink-0 flex border-t p-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="flex items-center w-full justify-start px-2"
                    >
                      <div className="flex items-center">
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarImage
                            src={
                              userProfile
                                ? `https://xvatar.vercel.app/api/avatar/${userProfile.email}.svg?rounded=120&size=240`
                                : undefined
                            }
                            alt={getDisplayName()}
                          />
                          <AvatarFallback>
                            {userProfile
                              ? getInitials(
                                  userProfile.first_name,
                                  userProfile.last_name,
                                )
                              : 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="text-sm text-left mr-2">
                          <div className="font-medium capitalize">
                            {isLoadingProfile ? (
                              <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-16" />
                              </div>
                            ) : (
                              getDisplayName()
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground/70 truncate">
                            {isLoadingProfile ? (
                              <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-36" />
                              </div>
                            ) : (
                              userProfile?.email
                            )}
                          </div>
                        </div>
                        <ChevronDown className="h-4 w-4 ml-auto" />
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {getDisplayName()}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {userProfile?.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Mobile navigation */}
          <Sheet open={isMobileNavOpen} onOpenChange={setIsMobileNavOpen}>
            <SheetContent side="left" className="w-64 p-0">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-4 border-b">
                  <span className="text-xl font-semibold">Smart Reception</span>
                </div>
                <nav className="flex-1 space-y-1 p-2">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        pathname === item.href
                          ? 'bg-muted text-foreground'
                          : 'text-muted-foreground hover:bg-accent hover:text-foreground',
                        'group flex items-center px-2 py-2 text-sm font-medium rounded-md',
                      )}
                      onClick={() => setIsMobileNavOpen(false)}
                    >
                      <item.icon
                        className={cn(
                          pathname === item.href
                            ? 'text-muted-foreground/70'
                            : 'text-muted-foreground/50 group-hover:text-muted-foreground/70',
                          'mr-3 h-5 w-5 flex-shrink-0',
                        )}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  ))}
                </nav>
                <div className="flex-shrink-0 flex border-t border-border p-4">
                  <div className="flex items-center w-full">
                    <Avatar className="h-8 w-8 mr-2">
                      <AvatarImage
                        src={
                          userProfile
                            ? `https://xvatar.vercel.app/api/avatar/${userProfile.email}.svg?rounded=120&size=240`
                            : 'https://xvatar.vercel.app/api/avatar/xvatar.svg?rounded=120&size=240'
                        }
                        alt={getDisplayName()}
                      />
                      <AvatarFallback>
                        {userProfile
                          ? getInitials(
                              userProfile.first_name,
                              userProfile.last_name,
                            )
                          : 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="text-sm">
                      <div className="font-medium">
                        {!isLoadingProfile ? (
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-4 w-16" />
                          </div>
                        ) : (
                          getDisplayName()
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground/70">
                        {userProfile?.email || 'User'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          {/* Main content */}
          <div className="flex flex-1 flex-col overflow-hidden">
            {/* Top navigation */}
            <div className="flex backdrop-blur bg-transparent shadow-sm z-10 h-16 items-center justify-between px-4 border-b border-border/50">
              <div className="flex">
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setIsMobileNavOpen(true)}
                >
                  <Menu className="h-6 w-6" />
                </Button>
              </div>
              <div className="flex items-center space-x-4">
                <AdminClinicDropdown />
                <ThemeToggle />
                {/* <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative">
                      <Bell className="h-5 w-5" />
                      <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center">
                        3
                      </Badge>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80">
                    <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="max-h-80 overflow-y-auto">
                      {[1, 2, 3].map((i) => (
                        <DropdownMenuItem key={i} className="py-2">
                          <div className="flex items-start">
                            <ClipboardList className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground/70" />
                            <div>
                              <p className="font-medium">
                                New staff invitation accepted
                              </p>
                              <p className="text-xs text-muted-foreground/70">
                                Dr. Sarah Johnson has accepted your invitation
                              </p>
                              <p className="text-xs text-muted-foreground/50 mt-1">
                                {i === 1
                                  ? 'Just now'
                                  : i === 2
                                    ? '2 hours ago'
                                    : 'Yesterday'}
                              </p>
                            </div>
                          </div>
                        </DropdownMenuItem>
                      ))}
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="justify-center">
                      <Button variant="ghost" className="w-full">
                        View all notifications
                      </Button>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu> */}
                <div className="md:hidden">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={
                        userProfile
                          ? `https://xvatar.vercel.app/api/avatar/${userProfile.email}.svg?rounded=120&size=240`
                          : undefined
                      }
                      alt={getDisplayName()}
                    />
                    <AvatarFallback>
                      {userProfile
                        ? getInitials(
                            userProfile.first_name,
                            userProfile.last_name,
                          )
                        : 'U'}
                    </AvatarFallback>
                  </Avatar>
                </div>
              </div>
            </div>

            {/* Page content */}
            <main className="flex-1 overflow-y-auto bg-muted/10 p-4 md:p-6">
              {children}
            </main>
          </div>
        </div>
      </AdminClinicProvider>
    </ProtectedRoute>
  );
}
