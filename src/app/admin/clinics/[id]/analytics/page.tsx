'use client';
import { getClinicAnalytics } from '@/actions/admin';
import type { RetellCall, RetellCallListResponse } from '@/lib/analytics-types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDistanceToNow, format } from 'date-fns';
import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';
import { fetchAuthSession } from 'aws-amplify/auth';

interface AnalyticsResult {
  ok: boolean;
  status: number;
  data?: RetellCallListResponse;
  error?: string | null;
}

export default function ClinicAnalyticsPage() {
  const params = {
    id: '123', // todo
  };

  const [loading, setLoading] = useState(true);
  const [result, setResult] = useState<AnalyticsResult>({
    ok: false,
    status: 0,
    data: undefined,
    error: null,
  });
  const [calls, setCalls] = useState<RetellCall[]>([]);

  useEffect(() => {
    async function fetchAnalytics() {
      setLoading(true);
      try {
        // Get token from Amplify auth session
        const session = await fetchAuthSession({ forceRefresh: true });
        const token = session.tokens?.accessToken?.toString() || '';
        console.log('Clinic ID:', params.id);
        // Using hardcoded clinic ID for now
        const analyticsResult = await getClinicAnalytics(
          token || '',
          '6822e9604b8b521b826a0528',
        );
        setResult(analyticsResult);

        if (analyticsResult.ok && analyticsResult.data?.data) {
          setCalls(analyticsResult.data.data);
        }
      } catch (error) {
        console.error('Error fetching analytics:', error);
        setResult({
          ok: false,
          error: 'Failed to fetch analytics data. Please try again.',
          status: 500,
        });
      } finally {
        setLoading(false);
      }
    }

    fetchAnalytics();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[60vh]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p>Loading call analytics...</p>
        </div>
      </div>
    );
  }

  if (!result.ok) {
    return (
      <div className="p-6">
        <h2 className="text-xl font-bold mb-2">Analytics</h2>
        <p className="text-red-500">
          {result.error || 'Failed to load analytics.'}
        </p>
      </div>
    );
  }

  // Calculate analytics metrics
  const totalCalls = calls.length;
  const endedCalls = calls.filter((call) => call.call_status === 'ended');
  const avgDuration =
    endedCalls.length > 0
      ? Math.round(
          endedCalls.reduce((sum, call) => sum + call.duration_ms, 0) /
            endedCalls.length /
            1000,
        )
      : 0;
  const successfulCalls = calls.filter(
    (call) => call.call_analysis?.call_successful,
  ).length;
  const successRate =
    totalCalls > 0 ? Math.round((successfulCalls / totalCalls) * 100) : 0;

  // Count by sentiment
  const sentimentCounts = calls.reduce(
    (acc, call) => {
      const sentiment = call.call_analysis?.user_sentiment || 'Unknown';
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold">Call Analytics</h2>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCalls}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgDuration}s</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sentiment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 text-sm">
              {Object.entries(sentimentCounts).map(([sentiment, count]) => (
                <Badge
                  key={sentiment}
                  className={
                    sentiment.toLowerCase().includes('positive')
                      ? 'bg-green-100 text-green-800'
                      : sentiment.toLowerCase().includes('negative')
                        ? 'bg-red-100 text-red-800'
                        : 'bg-blue-100 text-blue-800'
                  }
                >
                  {sentiment}: {count}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="detailed">Detailed View</TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          {calls.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <p>No call analytics found for this clinic.</p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="overflow-x-auto">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="px-4 py-2 text-left font-medium">
                          Call ID
                        </th>
                        <th className="px-4 py-2 text-left font-medium">
                          Type
                        </th>
                        <th className="px-4 py-2 text-left font-medium">
                          Status
                        </th>
                        <th className="px-4 py-2 text-left font-medium">
                          Start Time
                        </th>
                        <th className="px-4 py-2 text-left font-medium">
                          Duration
                        </th>
                        <th className="px-4 py-2 text-left font-medium">
                          Sentiment
                        </th>
                        <th className="px-4 py-2 text-left font-medium">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {calls.map((call) => (
                        <tr
                          key={call.call_id}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="px-4 py-2 font-mono text-xs">
                            {call.call_id.substring(0, 8)}...
                          </td>
                          <td className="px-4 py-2">{call.call_type}</td>
                          <td className="px-4 py-2">
                            <Badge
                              variant="outline"
                              className={
                                call.call_status === 'ended'
                                  ? 'bg-green-50 text-green-700 border-green-200'
                                  : 'bg-yellow-50 text-yellow-700 border-yellow-200'
                              }
                            >
                              {call.call_status}
                            </Badge>
                          </td>
                          <td className="px-4 py-2">
                            <div
                              title={format(
                                new Date(call.start_timestamp),
                                'PPpp',
                              )}
                            >
                              {formatDistanceToNow(
                                new Date(call.start_timestamp),
                                { addSuffix: true },
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-2">
                            {Math.round((call.duration_ms || 0) / 1000)}s
                          </td>
                          <td className="px-4 py-2">
                            <Badge
                              className={
                                call.call_analysis?.user_sentiment
                                  ?.toLowerCase()
                                  .includes('positive')
                                  ? 'bg-green-100 text-green-800'
                                  : call.call_analysis?.user_sentiment
                                        ?.toLowerCase()
                                        .includes('negative')
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-blue-100 text-blue-800'
                              }
                            >
                              {call.call_analysis?.user_sentiment || 'Unknown'}
                            </Badge>
                          </td>
                          <td className="px-4 py-2">
                            <div className="flex gap-2">
                              {call.recording_url && (
                                <a
                                  href={call.recording_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 underline text-xs"
                                >
                                  Recording
                                </a>
                              )}
                              {call.public_log_url && (
                                <a
                                  href={call.public_log_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 underline text-xs"
                                >
                                  Log
                                </a>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="detailed">
          {calls.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <p>No call analytics found for this clinic.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {calls.map((call) => (
                <Card key={call.call_id}>
                  <CardHeader>
                    <div className="flex justify-between">
                      <CardTitle className="text-lg">
                        Call {call.call_id.substring(0, 8)}...
                      </CardTitle>
                      <Badge
                        variant="outline"
                        className={
                          call.call_analysis?.call_successful
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : 'bg-red-50 text-red-700 border-red-200'
                        }
                      >
                        {call.call_analysis?.call_successful
                          ? 'Successful'
                          : 'Unsuccessful'}
                      </Badge>
                    </div>
                    <CardDescription>
                      {format(new Date(call.start_timestamp), 'PPpp')} •
                      Duration: {Math.round((call.duration_ms || 0) / 1000)}s
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-1">Summary</h4>
                      <p className="text-sm">
                        {call.call_analysis?.call_summary ||
                          'No summary available'}
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold mb-1">Call Details</h4>
                        <ul className="text-sm space-y-1">
                          <li>
                            <span className="text-gray-500">Type:</span>{' '}
                            {call.call_type}
                          </li>
                          <li>
                            <span className="text-gray-500">Status:</span>{' '}
                            {call.call_status}
                          </li>
                          <li>
                            <span className="text-gray-500">Direction:</span>{' '}
                            {call.direction}
                          </li>
                          <li>
                            <span className="text-gray-500">From:</span>{' '}
                            {call.from_number || 'Unknown'}
                          </li>
                          <li>
                            <span className="text-gray-500">To:</span>{' '}
                            {call.to_number || 'Unknown'}
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-1">Analysis</h4>
                        <ul className="text-sm space-y-1">
                          <li>
                            <span className="text-gray-500">Sentiment:</span>{' '}
                            {call.call_analysis?.user_sentiment || 'Unknown'}
                          </li>
                          <li>
                            <span className="text-gray-500">In Voicemail:</span>{' '}
                            {call.call_analysis?.in_voicemail ? 'Yes' : 'No'}
                          </li>
                          <li>
                            <span className="text-gray-500">
                              Disconnection:
                            </span>{' '}
                            {call.disconnection_reason || 'N/A'}
                          </li>
                          <li>
                            <span className="text-gray-500">Cost:</span> $
                            {call.call_cost?.combined_cost.toFixed(4) || '0.00'}
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      {call.recording_url && (
                        <a
                          href={call.recording_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline text-sm"
                        >
                          Listen to Recording
                        </a>
                      )}
                      {call.public_log_url && (
                        <a
                          href={call.public_log_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline text-sm"
                        >
                          View Call Log
                        </a>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
