Admin-

4. Average duration by hour & call volume graphs are not honoring selected tab -> Comment
5. Refresh CTA didn't refresh API
6. Monthly Call Trend: X axis show 11 May like date -> Check in Client dashboard as well
7. Labels in Pie charts like Call success / Customer sentiment overlaps and become unreadable -> Check in Client dashboard as well
8. Diagnostic Services in Clinic details: Remove Status and make it editable -> Client
9. CRM Details tab is missing in Clinic Profile -> Name, Auth Details, Custom Details fields
   name : CLINIKO/GENIE
10. Clinic Profile -> Agent Details

- Under voice configuration: Remove temperature, speed & volume
- Remove DTMF Options
- Under call settings: remove "Voicemail Detection Timeout"
- Remove edit configuration CTA
- Integrate Create voice agent API -> super imp
  POST: https://app.smartreception.ai/api/retell/voice-agent?clinic_id=6822e9604b8b521b826a0528
  {}
  Keep agent details tab at last

13. Clinic Profile Tabs: Cursor pointer
14. Clinic Profile: KB Details

- Simplify Layout: Remove tabs (Existing KB & Create New)
- Instead make it sequential, Create button always at bottom
- Remove URls & text content options, <PERSON><PERSON> can only be craeted via upload file

Upload: https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=6822e9604b8b521b826a0528  
 form data: files 10. Manage Users -> Remove & Change role CTAs alignment with icons

- Also integrate below APIs, error message show from BE
  - Change Role:
  - Remove:

11. Manage admins: Integrate remove admin & add admin popup

- Remove admin: /api/admins/remove-admin payload { uid: string; }
- add admin: /api/admins/register-admin payload { uid: string; }

export interface IClinic extends Document {
clinic_name: string;
clinic_addresses?: Array<{
address: string;
full_address: string;
business_location_id: string; // Business ID in CLINIKO
}>;
clinic_email: string;
clinic_phone?: string;
clinic_website?: string;
agent_id?: string;
crm_details: {
name: string;
auth_details: {
api_key: string;
api_endpoint?: string;
};
custom_fields: {
appointment_type_id: string;
};
};
human_transfer_destination_number?: string;
smart_reception_phone_no?: string;
created_at: Date;
updated_at: Date;
is_active: boolean;
\_id: string;
diagnostic_services?: Array<{
name: string;
is_referral_required: boolean;
}>;
}

Client-

2. Invite Staff -> Logout / new user state persistence
- Also integrate below APIs- show error message from BE
  - Change Role: POST /api/db/user-clinic-roles (pass {user_id, clinic_id, role }) in body
  - Remove: DELETE /api/db/user-clinic-roles (pass {user_id, clinic_id }) in body
